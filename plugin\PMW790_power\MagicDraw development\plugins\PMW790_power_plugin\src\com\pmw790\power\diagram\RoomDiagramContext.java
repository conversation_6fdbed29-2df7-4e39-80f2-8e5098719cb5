package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.uml.BaseElement;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.LiteralReal;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Type;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.VisibilityKindEnum;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.AggregationKindEnum;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdports.Port;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.functions.Utilities;

import java.util.*;

/**
 * Context class holding information needed for room power diagram creation.
 * This manages multiple cabinets within a room.
 * Extends PowerDiagramContext to inherit shared functionality.
 */
public class RoomDiagramContext extends PowerDiagramContext {
    private final Class roomBlock;
    private final List<String> cabinetNames;
    private final Map<String, CabinetDiagramContext> cabinetContexts;
    private final Set<String> cabinetsWithProviders;
    private final Map<String, List<String>> providerHierarchyCache;
    private final Map<String, Property> roomPowerProviderPropertiesCache;

    // Cabinet consumer cache for efficient consumer lookups
    private final Map<String, Map<String, Property>> cabinetConsumerCache;

    // Track cabinet presentation elements for efficient consumer placement
    private final Map<String, PresentationElement> cabinetPresentationElements;

    // Track cabinets that have consumers (regardless of whether they have providers)
    private final Set<String> cabinetsWithConsumers;

    // Cache for room-level consumer value properties (Consumer Name -> Property Name -> Property)
    private final Map<String, Map<String, Property>> roomConsumerValuePropertiesCache;

    // Track consumer presentation elements for efficient reuse (Consumer Name -> PresentationElement)
    private final Map<String, PresentationElement> consumerPresentationElements;

    // Cache for room-level provider value properties (Provider Name -> Property Name -> Property)
    private final Map<String, Map<String, Property>> roomProviderValuePropertiesCache;

    // Track provider presentation elements for efficient reuse (Provider Name -> PresentationElement)
    private final Map<String, PresentationElement> providerPresentationElements;

    // Cache for room-level constraint properties (Provider Name -> Constraint Type -> Property)
    private final Map<String, Map<String, Property>> roomConstraintPropertiesCache;

    /**
     * Creates a new context for room diagram operations
     */
    public RoomDiagramContext(Project project, Class roomBlock, ConnectionRegistry registry) {
        // Call parent constructor with room name as context name
        super(project, registry, (roomBlock != null) ? roomBlock.getName() : null);

        this.roomBlock = roomBlock;
        this.cabinetNames = registry.getCabinetsForRoom(this.contextName);
        this.cabinetContexts = new HashMap<>();
        this.cabinetsWithProviders = new HashSet<>();
        this.providerHierarchyCache = new HashMap<>();
        this.roomPowerProviderPropertiesCache = new HashMap<>();
        this.cabinetConsumerCache = new HashMap<>();
        this.cabinetPresentationElements = new HashMap<>();
        this.cabinetsWithConsumers = new HashSet<>();
        this.roomConsumerValuePropertiesCache = new HashMap<>();
        this.consumerPresentationElements = new HashMap<>();
        this.roomProviderValuePropertiesCache = new HashMap<>();
        this.providerPresentationElements = new HashMap<>();
        this.roomConstraintPropertiesCache = new HashMap<>();

        // Pre-cache provider hierarchy for faster access
        precacheProviderHierarchy();

        // Pre-identify cabinets with providers for faster filtering
        identifyCabinetsWithProviders();

        // Pre-cache cabinet consumers for efficient consumer lookups
        initializeCabinetConsumerCache();
    }

    // Implementation of abstract methods from PowerDiagramContext

    @Override
    public Class getContextBlock() {
        return roomBlock;
    }

    @Override
    public List<String> getProviders() {
        // Return room-level providers (parent providers from hierarchy cache)
        return new ArrayList<>(providerHierarchyCache.keySet());
    }

    @Override
    public Property getPartProperty(String propertyName) {
        // For room context, get properties from room non-cabinet properties
        try {
            Map<String, Property> allNonCabinetProps = Utilities.getRoomNonCabinetProperties(this.contextName);
            return allNonCabinetProps.get(propertyName);
        } catch (Exception e) {
            Log("Error getting part property for room element " + propertyName + ": " + e.getMessage());
            return null;
        }
    }

    @Override
    public String getContextType() {
        return "ROOM";
    }

    @Override
    public boolean isRoomContext() {
        return true; // Room context is always a room context
    }

    @Override
    public List<String> getConsumersForProvider(String providerName) {
        // For room context, use existing method
        return getConsumersForRoomProvider(providerName);
    }

    @Override
    public boolean isTopProvider(String providerName) {
        // In room context, parent providers in the hierarchy cache are considered top providers
        if (providerName == null) {
            return false;
        }
        return providerHierarchyCache.containsKey(providerName);
    }

    @Override
    public Map<String, Property> getConstraintPorts(String elementName, String constraintType) {
        // For room context, get constraint ports from room constraint properties
        Map<String, Property> constraintPorts = new HashMap<>();

        if (elementName == null || constraintType == null) {
            return constraintPorts;
        }

        try {
            Map<String, Property> constraintProperties = getConstraintProperties(elementName);
            Property constraintProperty = constraintProperties.get(constraintType);

            if (constraintProperty != null && constraintProperty.getType() instanceof Class) {
                Class constraintBlock = (Class) constraintProperty.getType();

                // Find ports in the constraint block
                for (Property property : constraintBlock.getOwnedAttribute()) {
                    if (property instanceof Port) {
                        constraintPorts.put(property.getName(), property);
                    }
                }
            }
        } catch (Exception e) {
            Log("Error getting constraint ports for room provider " + elementName +
                " constraint " + constraintType + ": " + e.getMessage());
        }

        return constraintPorts;
    }

    @Override
    public void cacheConstraintPorts(String elementName, String constraintType, Map<String, Property> ports) {
        // Room-specific constraint ports caching
        cacheRoomConstraintPorts(elementName, constraintType, ports);
    }

    /**
     * Pre-caches the provider hierarchy data for this room
     */
    private void precacheProviderHierarchy() {
        Map<String, Map<String, List<String>>> roomToProvidersMap = registry.getRoomToPowerProviders();
        Map<String, List<String>> roomHierarchy = roomToProvidersMap.get(this.contextName);

        if (roomHierarchy != null) {
            providerHierarchyCache.putAll(roomHierarchy);
        }
    }

    /**
     * Pre-identifies cabinets that have power providers with consumers
     */
    private void identifyCabinetsWithProviders() {
        // If there are no cabinets, we can return early
        if (cabinetNames.isEmpty()) {
            return;
        }

        for (String cabinetName : cabinetNames) {
            List<String> providers = registry.getCabinetToPowerProviders().getOrDefault(cabinetName, Collections.emptyList());
            if (!providers.isEmpty()) {
                cabinetsWithProviders.add(cabinetName);
            }
        }
    }

    /**
     * Pre-caches cabinet consumer properties for efficient consumer lookups
     * This builds a map of cabinet name to consumer properties within that cabinet
     * Also identifies cabinets that have consumers (regardless of whether they have providers)
     */
    private void initializeCabinetConsumerCache() {
        // If there are no cabinets, we can return early
        if (cabinetNames.isEmpty()) {
            return;
        }

        for (String cabinetName : cabinetNames) {
            Map<String, Property> cabinetConsumers = new HashMap<>();

            // Get all properties for this cabinet from the cache
            Map<String, Property> cabinetProperties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);

            if (cabinetProperties != null) {
                // Filter for power consumers only
                for (Map.Entry<String, Property> entry : cabinetProperties.entrySet()) {
                    String propertyName = entry.getKey();
                    Property property = entry.getValue();

                    // Check if this property is a power consumer using the registry's element type map
                    String elementType = registry.getElementType(propertyName);
                    if (Utilities.TYPE_POWER_CONSUMER.equals(elementType)) {
                        cabinetConsumers.put(propertyName, property);
                    }
                }
            }

            // Store the consumer properties for this cabinet (even if empty)
            cabinetConsumerCache.put(cabinetName, cabinetConsumers);

            // Track cabinets that have consumers
            if (!cabinetConsumers.isEmpty()) {
                cabinetsWithConsumers.add(cabinetName);
            }
        }
    }

    /**
     * Gets all cabinet contexts for this room
     * @return List of CabinetDiagramContext objects
     */
    public List<CabinetDiagramContext> getAllCabinetContexts() {
        List<CabinetDiagramContext> contexts = new ArrayList<>();

        for (String cabinetName : cabinetNames) {
            CabinetDiagramContext context = getCabinetContext(cabinetName);
            if (context != null) {
                contexts.add(context);
            }
        }

        return contexts;
    }

    /**
     * Gets a CabinetDiagramContext for a specific cabinet in the room
     * This reuses the pre-cached properties for efficiency
     *
     * @param cabinetName The name of the cabinet
     * @return A CabinetDiagramContext for the cabinet, or null if not found
     */
    public CabinetDiagramContext getCabinetContext(String cabinetName) {
        // Check if we already have a context for this cabinet
        if (cabinetContexts.containsKey(cabinetName)) {
            return cabinetContexts.get(cabinetName);
        }

        try {
            // Use cached cabinet block instead of looking it up again
            Class cabinetBlock = registry.getCabinetBlockByName(cabinetName);
            if (cabinetBlock != null) {
                // Get properties from the global cache
                Map<String, Property> properties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);

                // Create context with pre-cached properties
                CabinetDiagramContext context = new CabinetDiagramContext(
                        project, cabinetBlock, registry, true, this.contextName, properties);

                // Store in cache
                cabinetContexts.put(cabinetName, context);
                return context;
            } else {
                Log("Warning: Cabinet block not found in cache for " + cabinetName + " in room " + this.contextName);
            }
        } catch (Exception e) {
            Log("Error creating cabinet context for " + cabinetName + ": " + e.getMessage());
        }

        return null;
    }

    /**
     * @return The room name
     */
    public String getRoomName() {
        return this.contextName;
    }

    /**
     * @return Number of cabinets in this room
     */
    public int getCabinetCount() {
        return cabinetNames.size();
    }

    /**
     * Gets the list of room-level power providers
     * @return List of room-level provider names
     */
    public List<String> getRoomProviders() {
        // Use cached hierarchy data to get parent providers
        return new ArrayList<>(providerHierarchyCache.keySet());
    }

    /**
     * Adds cabinets and room-level power providers to the room diagram
     * Processing order: 1) Cabinets with providers, 2) Room-level providers with cabinet consumer connections,
     * 3) Constraint properties for room providers, 4) External load properties, 5) Binding connectors,
     * 6) Value properties for consumers and providers
     *
     * @param diagram The diagram to add elements to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    public void addToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        if (diagram == null) {
            Log("Error: Cannot add elements to null diagram");
            return;
        }

        // STEP 1: Add cabinets with providers
        addCabinetsWithProvidersToRoomDiagram(diagram);

        // STEP 2: Add room-level power elements and connect to unconnected cabinet consumers
        Map<String, Property> allNonCabinetProps = Utilities.getRoomNonCabinetProperties(this.contextName);
        addPowerElementsToRoomDiagram(diagram, allNonCabinetProps);

        // STEP 3: Add constraint properties for room-level providers
        Map<String, Property> roomProviderProps = getRoomPowerProviderProperties(allNonCabinetProps);
        if (!roomProviderProps.isEmpty()) {
            addConstraintPropertiesToRoomDiagram(diagram, roomProviderProps);

            // STEP 4: Add external load properties for room providers
            addExternalLoadPropertiesToRoomDiagram(diagram, roomProviderProps);
        }

        // STEP 5: Create binding connectors using PowerConnectorManager
        try {
            PowerConnectorManager.createRoomBindingConnectors(this, diagram);
        } catch (Exception e) {
            Log("Error creating room binding connectors: " + e.getMessage());
        }

        // STEP 6: Add value properties using PowerDiagramElementManager approach
        addValuePropertiesToRoomDiagram(diagram, allNonCabinetProps, roomProviderProps);
    }

    /**
     * Adds value properties to room diagram elements using PowerDiagramElementManager approach
     * @param diagram The diagram
     * @param allNonCabinetProps All non-cabinet properties
     * @param roomProviderProps Room provider properties
     */
    private void addValuePropertiesToRoomDiagram(DiagramPresentationElement diagram,
                                                Map<String, Property> allNonCabinetProps,
                                                Map<String, Property> roomProviderProps) {
        try {
            // Add value properties to consumers and providers using the same approach as PowerDiagramElementManager
            Map<String, Map<String, Object>> allElementProps = registry.getElementProperties();

            // Process consumers
            Map<String, Property> roomConsumerProps = getRoomPowerConsumerProperties(allNonCabinetProps);
            for (Map.Entry<String, Property> entry : roomConsumerProps.entrySet()) {
                String consumerName = entry.getKey();
                Property consumerProperty = entry.getValue();

                if (isConsumerConnectedToProvider(consumerName)) {
                    addValuePropertiesToElement(diagram, consumerName, consumerProperty, allElementProps);
                }
            }

            // Process providers (only child providers and leaf providers)
            for (String parentProviderName : roomProviderProps.keySet()) {
                List<String> childProviders = providerHierarchyCache.get(parentProviderName);

                if (childProviders != null && !childProviders.isEmpty()) {
                    // Parent has children - add value properties for each child provider
                    for (String childProviderName : childProviders) {
                        Property childProperty = getPartProperty(childProviderName);
                        if (childProperty != null) {
                            addValuePropertiesToElement(diagram, childProviderName, childProperty, allElementProps);
                        }
                    }
                } else {
                    // Parent has no children - it's a leaf provider, add value properties for it
                    Property providerProperty = roomProviderProps.get(parentProviderName);
                    addValuePropertiesToElement(diagram, parentProviderName, providerProperty, allElementProps);
                }
            }

        } catch (Exception e) {
            Log("Error adding value properties to room diagram: " + e.getMessage());
        }
    }

    /**
     * Adds value properties to a specific element using PowerDiagramElementManager approach
     * @param diagram The diagram
     * @param elementName The element name
     * @param elementProperty The element property
     * @param allElementProps All element properties from registry
     */
    private void addValuePropertiesToElement(DiagramPresentationElement diagram, String elementName,
                                           Property elementProperty, Map<String, Map<String, Object>> allElementProps) {
        if (!(elementProperty.getType() instanceof Class)) {
            return;
        }

        Class elementType = (Class) elementProperty.getType();

        // Find the element's presentation element in the diagram
        PresentationElement elementPE = findPresentationElementForProperty(diagram, elementProperty);
        if (elementPE == null) {
            return;
        }

        // Get element properties from registry
        Map<String, Object> elementRegistryProps = allElementProps.get(elementName);
        if (elementRegistryProps == null || elementRegistryProps.isEmpty()) {
            return;
        }

        // Collect value properties to display (same logic as PowerDiagramElementManager)
        List<Property> valueProps = new ArrayList<>();
        Map<String, Property> valuePropsMap = new HashMap<>();
        Set<String> propNames = elementRegistryProps.keySet();

        for (Property valueProperty : elementType.getOwnedAttribute()) {
            String propName = valueProperty.getName();
            if (!(valueProperty instanceof Port) && propName != null &&
                !propName.equals("host_asset") && !propName.equals("host_location") &&
                propNames.contains(propName)) {
                valueProps.add(valueProperty);
                valuePropsMap.put(propName, valueProperty);
            }
        }

        // Cache the value properties
        if (!valuePropsMap.isEmpty()) {
            String elementType_str = registry.getElementType(elementName);
            if (Utilities.TYPE_POWER_CONSUMER.equals(elementType_str)) {
                cacheConsumerValueProperties(elementName, valuePropsMap);
            } else if (Utilities.TYPE_POWER_PROVIDER.equals(elementType_str)) {
                cacheProviderValueProperties(elementName, valuePropsMap);
            }
        }

        // Add value properties to the presentation element
        PresentationElementsManager manager = PresentationElementsManager.getInstance();
        for (Property valueProperty : valueProps) {
            try {
                manager.createShapeElement(valueProperty, elementPE, true);
            } catch (Exception e) {
                Log("Error adding value property '" + valueProperty.getName() +
                    "' to element '" + elementName + "': " + e.getMessage());
            }
        }
    }

    /**
     * Adds total_power properties for cabinet top providers to the room diagram
     * This method processes all cabinet contexts and redefines total_power properties for their top providers
     *
     * @param diagram The diagram to add total_power properties to
     */
    public void addTopProviderTotalPowerProperties(DiagramPresentationElement diagram) {
        if (diagram == null) {
            Log("Error: Cannot add total_power properties to null diagram");
            return;
        }

        try {
            // Process each cabinet context
            for (CabinetDiagramContext cabinetContext : getAllCabinetContexts()) {
                String cabinetName = cabinetContext.getCabinetName();

                // Get top providers for this cabinet
                List<String> topProviders = cabinetContext.getTopProviders();

                if (topProviders.isEmpty()) {
                    continue;
                }

                // Process each top provider
                for (String topProviderName : topProviders) {
                    try {
                        addTotalPowerPropertyToTopProvider(diagram, cabinetContext, topProviderName);
                    } catch (Exception e) {
                        Log("Error adding total_power property for top provider " + topProviderName +
                            " in cabinet " + cabinetName + ": " + e.getMessage());
                        // Continue with other top providers
                    }
                }
            }
        } catch (Exception e) {
            Log("Error processing cabinet top provider total_power properties: " + e.getMessage());
        }
    }

    /**
     * Adds total_power property to a specific top provider in a cabinet
     * This method redefines the total_power property and adds it to the diagram
     *
     * @param diagram The diagram to add the property to
     * @param cabinetContext The cabinet context containing the top provider
     * @param topProviderName The name of the top provider
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addTotalPowerPropertyToTopProvider(DiagramPresentationElement diagram,
                                                   CabinetDiagramContext cabinetContext,
                                                   String topProviderName) throws ReadOnlyElementException {

        // Get the top provider property from the cabinet context
        Property topProviderProperty = cabinetContext.getPartProperty(topProviderName);
        if (topProviderProperty == null || !(topProviderProperty.getType() instanceof Class)) {
            Log("Warning: Could not find property or type for top provider: " + topProviderName);
            return;
        }

        Class topProviderType = (Class) topProviderProperty.getType();

        // Get registry properties for this provider
        Map<String, Object> providerRegistryProps = registry.getElementProperties().get(topProviderName);
        if (providerRegistryProps == null || !providerRegistryProps.containsKey("total_power")) {
            Log("Warning: No total_power property found in registry for top provider: " + topProviderName);
            return;
        }

        // Redefine the total_power property using the same pattern as PowerDiagramElementManager
        try {
            PowerDiagramElementManager.redefineInheritedPowerProperties(project, topProviderType, providerRegistryProps);
        } catch (Exception e) {
            Log("Error redefining total_power property for top provider " + topProviderName + ": " + e.getMessage());
            return;
        }

        // Find the top provider's presentation element in the diagram
        PresentationElement topProviderPE = findTopProviderPresentationElement(diagram, cabinetContext, topProviderProperty);
        if (topProviderPE == null) {
            Log("Warning: Could not find presentation element for top provider: " + topProviderName);
            return;
        }

        // Find the total_power property in the provider type
        Property totalPowerProperty = null;
        for (Property property : topProviderType.getOwnedAttribute()) {
            if ("total_power".equals(property.getName()) && !(property instanceof Port)) {
                totalPowerProperty = property;
                break;
            }
        }

        if (totalPowerProperty == null) {
            Log("Warning: total_power property not found in type for top provider: " + topProviderName);
            return;
        }

        // Add the total_power property to the diagram
        try {
            PresentationElementsManager manager = PresentationElementsManager.getInstance();
            manager.createShapeElement(totalPowerProperty, topProviderPE, true);
        } catch (Exception e) {
            Log("Error adding total_power property to diagram for top provider " + topProviderName + ": " + e.getMessage());
        }
    }

    /**
     * Finds the presentation element for a top provider in the room diagram
     * Uses the cached provider presentation elements for efficient lookup
     *
     * @param diagram The diagram to search in
     * @param cabinetContext The cabinet context containing the top provider
     * @param topProviderProperty The top provider property
     * @return The presentation element for the top provider, or null if not found
     */
    private PresentationElement findTopProviderPresentationElement(DiagramPresentationElement diagram,
                                                                  CabinetDiagramContext cabinetContext,
                                                                  Property topProviderProperty) {
        String topProviderName = topProviderProperty.getName();

        // Use the cached provider presentation elements for efficient lookup
        PresentationElement topProviderPE = providerPresentationElements.get(topProviderName);
        if (topProviderPE != null && topProviderPE.getElement() == topProviderProperty) {
            return topProviderPE;
        } else if (topProviderPE != null) {
            Log("Cached presentation element found but element mismatch for: " + topProviderName);
        } else {
            Log("No cached presentation element found for: " + topProviderName);
        }

        // Fallback: search within the cabinet's presentation element if cache lookup fails
        String cabinetName = cabinetContext.getCabinetName();
        PresentationElement cabinetPE = cabinetPresentationElements.get(cabinetName);
        if (cabinetPE != null) {
            for (PresentationElement childPE : cabinetPE.getPresentationElements()) {
                Element childElement = childPE.getElement();
                if (childElement == topProviderProperty) {
                    // Cache the found element for future use
                    providerPresentationElements.put(topProviderName, childPE);
                    return childPE;
                }
            }
        }

        Log("Warning: Could not find top provider presentation element for: " + topProviderName +
            " in cabinet: " + cabinetName);
        return null;
    }

    /**
     * Adds cabinets with power providers to the room diagram and identifies unconnected consumers
     * This method processes cabinets first and tracks consumers not connected to internal providers
     *
     * @param diagram The diagram to add cabinets to
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addCabinetsWithProvidersToRoomDiagram(DiagramPresentationElement diagram) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        for (String cabinetName : cabinetsWithProviders) {
            try {
                Map<String, Property> roomCabinetProps = Utilities.getRoomCabinetProperties(this.contextName);
                Property cabinetProperty = roomCabinetProps.get(cabinetName);
                CabinetDiagramContext cabinetContext = getCabinetContext(cabinetName);

                if (cabinetProperty != null && cabinetContext != null) {
                    PresentationElement cabinetPE = manager.createShapeElement(cabinetProperty, diagram, true);

                    cabinetPresentationElements.put(cabinetName, cabinetPE);

                    List<String> providers = cabinetContext.getTopProviders();
                    for (String providerName : providers) {
                        Property providerProperty = cabinetContext.getPartProperty(providerName);
                        if (providerProperty != null) {
                            PresentationElement providerPE = manager.createShapeElement(providerProperty, cabinetPE, true);
                            // Cache the provider presentation element for later use (e.g., adding total_power properties)
                            if (providerPE != null) {
                                providerPresentationElements.put(providerName, providerPE);
                            }
                        }
                    }

                    identifyUnconnectedConsumersInCabinet(cabinetName, cabinetContext);
                }
            } catch (Exception e) {
                Log("Error adding cabinet " + cabinetName + " to diagram: " + e.getMessage());
                // Continue with other cabinets
            }
        }
    }

    /**
     * Adds room-level power elements (providers with their hierarchy and associated consumers) to the diagram
     * Uses PowerDiagramElementManager for consistent element creation and property handling
     *
     * @param diagram The diagram to add power elements to
     * @param allNonCabinetProps Pre-retrieved non-cabinet properties for the room
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addPowerElementsToRoomDiagram(DiagramPresentationElement diagram, Map<String, Property> allNonCabinetProps) throws ReadOnlyElementException {
        // Get room-level power provider properties
        Map<String, Property> roomProviderProps = getRoomPowerProviderProperties(allNonCabinetProps);
        Map<String, Property> roomConsumerProps = getRoomPowerConsumerProperties(allNonCabinetProps);

        // Collect all elements to add to diagram
        List<Element> elementsToAdd = new ArrayList<>();
        Map<String, Map<String, Object>> propertiesToRedefine = new HashMap<>();
        Map<String, Map<String, Object>> allElementProps = registry.getElementProperties();

        // Process providers
        for (Map.Entry<String, Property> entry : roomProviderProps.entrySet()) {
            String providerName = entry.getKey();
            Property providerProperty = entry.getValue();

            elementsToAdd.add(providerProperty);

            // Add child providers under this parent using cached hierarchy
            addChildProviderElements(providerProperty, elementsToAdd);

            // Prepare property redefinition for this provider
            if (providerProperty.getType() instanceof Class) {
                Class providerType = (Class) providerProperty.getType();
                Map<String, Object> providerRegistryProps = allElementProps.get(providerName);
                if (providerRegistryProps != null && !providerRegistryProps.isEmpty()) {
                    propertiesToRedefine.put(providerType.getID(), providerRegistryProps);
                }
            }
        }

        // Process consumers
        for (Map.Entry<String, Property> entry : roomConsumerProps.entrySet()) {
            String consumerName = entry.getKey();
            Property consumerProperty = entry.getValue();

            // Only add consumers that are connected to providers
            if (isConsumerConnectedToProvider(consumerName)) {
                elementsToAdd.add(consumerProperty);

                // Prepare property redefinition for this consumer
                if (consumerProperty.getType() instanceof Class) {
                    Class consumerType = (Class) consumerProperty.getType();
                    Map<String, Object> consumerRegistryProps = allElementProps.get(consumerName);
                    if (consumerRegistryProps != null && !consumerRegistryProps.isEmpty()) {
                        propertiesToRedefine.put(consumerType.getID(), consumerRegistryProps);
                    }
                }
            }
        }

        // Add all elements to diagram using PowerDiagramElementManager
        if (!elementsToAdd.isEmpty()) {
            PowerDiagramElementManager.addElementsToDiagram(null, diagram,
                elementsToAdd.toArray(new Element[0]));
        }

        // Redefine properties in batch using PowerDiagramElementManager
        for (Map.Entry<String, Map<String, Object>> entry : propertiesToRedefine.entrySet()) {
            String elementId = entry.getKey();
            Map<String, Object> props = entry.getValue();
            try {
                BaseElement baseElement = project.getElementByID(elementId);
                if (baseElement instanceof Element) {
                    Element element = (Element) baseElement;
                    PowerDiagramElementManager.redefineInheritedPowerProperties(project, element, props);
                }
            } catch (Exception e) {
                Log("Error redefining properties for element ID " + elementId + ": " + e.getMessage());
            }
        }

        // Cache presentation elements for efficient reuse
        cachePresentationElements(diagram, roomProviderProps, roomConsumerProps);
    }

    /**
     * Adds child provider elements to the collection for diagram addition
     * @param parentProviderProperty The parent provider property
     * @param elementsToAdd The collection to add child elements to
     */
    private void addChildProviderElements(Property parentProviderProperty, List<Element> elementsToAdd) {
        if (!(parentProviderProperty.getType() instanceof Class)) {
            return;
        }

        String parentProviderName = parentProviderProperty.getName();
        Class parentProviderBlock = (Class) parentProviderProperty.getType();

        // Use cached hierarchy data to get child providers
        List<String> childProviders = providerHierarchyCache.get(parentProviderName);

        if (childProviders != null && !childProviders.isEmpty()) {
            for (String childProviderName : childProviders) {
                try {
                    // Find child provider part property in parent provider block
                    Property childProviderProperty = Utilities.ModelElements.findPropertyByName(parentProviderBlock, childProviderName);
                    if (childProviderProperty != null) {
                        elementsToAdd.add(childProviderProperty);
                    }
                } catch (Exception e) {
                    Log("Error adding child provider " + childProviderName + " under parent " + parentProviderName + ": " + e.getMessage());
                }
            }
        }
    }

    /**
     * Caches presentation elements for efficient reuse
     * @param diagram The diagram containing the presentation elements
     * @param roomProviderProps Map of provider properties
     * @param roomConsumerProps Map of consumer properties
     */
    private void cachePresentationElements(DiagramPresentationElement diagram,
                                         Map<String, Property> roomProviderProps,
                                         Map<String, Property> roomConsumerProps) {
        try {
            // Cache provider presentation elements
            for (String providerName : roomProviderProps.keySet()) {
                PresentationElement providerPE = findPresentationElementForProperty(diagram, roomProviderProps.get(providerName));
                if (providerPE != null) {
                    providerPresentationElements.put(providerName, providerPE);
                }
            }

            // Cache consumer presentation elements
            for (String consumerName : roomConsumerProps.keySet()) {
                PresentationElement consumerPE = findPresentationElementForProperty(diagram, roomConsumerProps.get(consumerName));
                if (consumerPE != null) {
                    consumerPresentationElements.put(consumerName, consumerPE);
                }
            }
        } catch (Exception e) {
            Log("Error caching presentation elements: " + e.getMessage());
        }
    }

    /**
     * Finds the presentation element for a given property in the diagram
     * @param diagram The diagram to search
     * @param property The property to find
     * @return The presentation element or null if not found
     */
    private PresentationElement findPresentationElementForProperty(DiagramPresentationElement diagram, Property property) {
        if (diagram == null || property == null) {
            return null;
        }

        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe.getElement() == property) {
                return pe;
            }
            // Also check nested elements (for child providers)
            for (PresentationElement childPE : pe.getPresentationElements()) {
                if (childPE.getElement() == property) {
                    return childPE;
                }
            }
        }
        return null;
    }

    /**
     * Gets room-level power provider properties from the room element cache
     * Optimized version that uses pre-retrieved non-cabinet properties
     *
     * @param nonCabinetProps Pre-retrieved non-cabinet properties for the room
     * @return Map of provider name to Property object for room-level providers
     */
    private Map<String, Property> getRoomPowerProviderProperties(Map<String, Property> nonCabinetProps) {
        // Use cached data if available
        if (!roomPowerProviderPropertiesCache.isEmpty()) {
            return new HashMap<>(roomPowerProviderPropertiesCache);
        }

        Map<String, Property> roomProviderProps = new HashMap<>();

        // Get parent providers from cached hierarchy
        Set<String> roomParentProviders = providerHierarchyCache.keySet();

        if (roomParentProviders.isEmpty()) {
            return roomProviderProps;
        }

        // Filter for power providers that are parent providers in this room
        for (Map.Entry<String, Property> entry : nonCabinetProps.entrySet()) {
            String elementName = entry.getKey();
            if (roomParentProviders.contains(elementName)) {
                roomProviderProps.put(elementName, entry.getValue());
            }
        }

        // Cache the results for future use
        roomPowerProviderPropertiesCache.putAll(roomProviderProps);

        return roomProviderProps;
    }

    /**
     * Adds child power providers under their parent provider
     * OPTIMIZED: Uses pre-retrieved parent provider property to avoid redundant lookups
     *
     * @param diagram The diagram
     * @param parentPE The parent provider presentation element
     * @param parentProviderName The parent provider name
     * @param parentProviderProperty The parent provider property (already retrieved)
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addChildProvidersToParent(DiagramPresentationElement diagram, PresentationElement parentPE,
                                           String parentProviderName, Property parentProviderProperty) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // Use cached hierarchy data instead of fetching from registry again
        List<String> childProviders = providerHierarchyCache.get(parentProviderName);

        if (childProviders != null && !childProviders.isEmpty()) {
            for (String childProviderName : childProviders) {
                try {
                    // Get the parent provider block to find child part properties
                    Set<String> allProviders = registry.getPowerProviders();
                    if (allProviders.contains(parentProviderName)) {
                        // Use the already-retrieved parent provider property
                        if (parentProviderProperty != null && parentProviderProperty.getType() instanceof Class) {
                            Class parentProviderBlock = (Class) parentProviderProperty.getType();

                            // Find child provider part property in parent provider block
                            Property childProviderProperty = Utilities.ModelElements.findPropertyByName(parentProviderBlock, childProviderName);

                            if (childProviderProperty != null) {
                                manager.createShapeElement(childProviderProperty, parentPE, true);
                            }
                        }
                    }
                } catch (Exception e) {
                    Log("Error adding power provider " + childProviderName + " under parent " + parentProviderName + ": " + e.getMessage());
                }
            }
        }
    }

    /**
     * Adds power consumers connected to a specific provider to the diagram
     * Handles both room-level consumers and cabinet consumers
     * For parent providers, also includes consumers connected to child providers
     *
     * @param diagram The diagram to add consumers to
     * @param providerName The provider name to find consumers for
     * @param allNonCabinetProps All non-cabinet properties in the room (includes consumers)
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addConsumersForProvider(DiagramPresentationElement diagram, String providerName,
                                       Map<String, Property> allNonCabinetProps) throws ReadOnlyElementException {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        List<String> connectedConsumers = new ArrayList<>();

        // Check if this is a parent provider with child providers
        List<String> childProviders = providerHierarchyCache.get(providerName);
        if (childProviders != null && !childProviders.isEmpty()) {
            // Parent provider: get consumers from child providers only
            for (String childProviderName : childProviders) {
                List<String> childConsumers = registry.getConsumersForProvider(childProviderName);
                connectedConsumers.addAll(childConsumers);
            }
        } else {
            connectedConsumers.addAll(registry.getConsumersForProvider(providerName));
        }

        // Process each connected consumer
        for (String consumerName : connectedConsumers) {
            Property consumerProperty = null;
            PresentationElement targetParent = diagram;

            // First, try to find the consumer in room-level non-cabinet properties
            consumerProperty = allNonCabinetProps.get(consumerName);

            // If not found in room-level properties, check if it's in any cabinet
            if (consumerProperty == null) {
                String cabinetName = findCabinetForConsumer(consumerName);
                if (cabinetName != null) {
                    consumerProperty = getConsumerFromCabinet(cabinetName, consumerName);

                    // Use the existing cabinet presentation element (created during cabinet processing)
                    if (consumerProperty != null) {
                        targetParent = cabinetPresentationElements.get(cabinetName);
                        if (targetParent == null) {
                            // Fallback: create cabinet presentation element if not found
                            targetParent = findOrCreateCabinetPresentationElement(diagram, cabinetName);
                            if (targetParent != null) {
                                cabinetPresentationElements.put(cabinetName, targetParent);
                            }
                        }
                    }
                }
            }

            // Add the consumer if found (either in room or cabinet)
            if (consumerProperty != null && targetParent != null) {
                try {
                    PresentationElement consumerPE = manager.createShapeElement(consumerProperty, targetParent, true);
                    // Cache the consumer presentation element for efficient reuse
                    if (consumerPE != null) {
                        consumerPresentationElements.put(consumerName, consumerPE);
                    }
                } catch (Exception e) {
                    Log("Error adding consumer " + consumerName + " for provider " + providerName + " to diagram: " + e.getMessage());
                }
            } else {
                Log("Warning: Could not find consumer property or target parent for " + consumerName);
            }
        }
    }

    /**
     * Gets the consumer property from a specific cabinet using the pre-cached data
     *
     * @param cabinetName The cabinet name
     * @param consumerName The consumer name
     * @return The consumer Property object, or null if not found
     */
    private Property getConsumerFromCabinet(String cabinetName, String consumerName) {
        if (cabinetName == null || consumerName == null) {
            return null;
        }

        Map<String, Property> cabinetConsumers = cabinetConsumerCache.get(cabinetName);
        if (cabinetConsumers != null) {
            return cabinetConsumers.get(consumerName);
        }

        return null;
    }

    /**
     * Finds the cabinet containing a specific consumer
     * This searches all cabinet consumers, not just unconnected ones
     *
     * @param consumerName The consumer name to search for
     * @return The cabinet name containing the consumer, or null if not found
     */
    private String findCabinetForConsumer(String consumerName) {
        if (consumerName == null) {
            return null;
        }

        // Search through all cabinet consumers
        for (Map.Entry<String, Map<String, Property>> entry : cabinetConsumerCache.entrySet()) {
            String cabinetName = entry.getKey();
            Map<String, Property> consumers = entry.getValue();

            if (consumers.containsKey(consumerName)) {
                return cabinetName;
            }
        }

        return null;
    }

    /**
     * Finds or creates a cabinet presentation element in the diagram
     * This ensures that consumers inside cabinets are properly nested under their cabinet
     *
     * @param diagram The room diagram
     * @param cabinetName The cabinet name
     * @return The cabinet presentation element, or null if cabinet cannot be found/created
     */
    private PresentationElement findOrCreateCabinetPresentationElement(DiagramPresentationElement diagram, String cabinetName) {
        if (diagram == null || cabinetName == null) {
            return null;
        }

        PresentationElementsManager manager = PresentationElementsManager.getInstance();

        // First, check if the cabinet presentation element already exists in the diagram
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe.getElement() instanceof Property) {
                Property property = (Property) pe.getElement();
                if (cabinetName.equals(property.getName())) {
                    return pe;
                }
            }
        }

        // If not found, create a new cabinet presentation element
        try {
            Map<String, Property> roomCabinetProps = Utilities.getRoomCabinetProperties(this.contextName);
            Property cabinetProperty = roomCabinetProps.get(cabinetName);

            if (cabinetProperty != null) {
                return manager.createShapeElement(cabinetProperty, diagram, true);
            }
        } catch (Exception e) {
            Log("Error creating cabinet presentation element for " + cabinetName + ": " + e.getMessage());
        }

        return null;
    }

    /**
     * Identifies consumers in a cabinet that are not connected to internal cabinet providers
     * These consumers may need to be connected to room-level providers
     *
     * @param cabinetName The cabinet name
     * @param cabinetContext The cabinet context containing provider and consumer information
     */
    private void identifyUnconnectedConsumersInCabinet(String cabinetName, CabinetDiagramContext cabinetContext) {
        if (cabinetName == null || cabinetContext == null) {
            return;
        }

        // Get all consumers in this cabinet
        Map<String, Property> cabinetConsumers = cabinetConsumerCache.get(cabinetName);
        if (cabinetConsumers == null || cabinetConsumers.isEmpty()) {
            return;
        }

        // Get all providers in this cabinet
        List<String> cabinetProviders = cabinetContext.getProviders();
        Set<String> connectedConsumers = new HashSet<>();

        // Find consumers connected to internal cabinet providers
        for (String providerName : cabinetProviders) {
            List<String> consumersForProvider = registry.getConsumersForProvider(providerName);
            connectedConsumers.addAll(consumersForProvider);
        }

        // Identify unconnected consumers (consumers in cabinet but not connected to internal providers)
        Set<String> unconnectedConsumers = new HashSet<>();
        for (String consumerName : cabinetConsumers.keySet()) {
            if (!connectedConsumers.contains(consumerName)) {
                unconnectedConsumers.add(consumerName);
            }
        }
    }

    /**
     * Caches value properties for a specific room-level consumer
     * @param consumerName The consumer name
     * @param valueProperties Map of property name to Property object
     */
    public void cacheConsumerValueProperties(String consumerName, Map<String, Property> valueProperties) {
        if (consumerName != null && valueProperties != null && !valueProperties.isEmpty()) {
            roomConsumerValuePropertiesCache.put(consumerName, new HashMap<>(valueProperties));
        }
    }

    /**
     * Caches value properties for a specific room-level provider
     * @param providerName The provider name
     * @param valueProperties Map of property name to Property object
     */
    public void cacheProviderValueProperties(String providerName, Map<String, Property> valueProperties) {
        if (providerName != null && valueProperties != null && !valueProperties.isEmpty()) {
            roomProviderValuePropertiesCache.put(providerName, new HashMap<>(valueProperties));
        }
    }

    /**
     * Caches a constraint property for a specific room-level provider
     * @param providerName The provider name
     * @param constraintType The constraint type (e.g., "Power_Total")
     * @param constraintProperty The constraint property to cache
     */
    public void cacheRoomConstraintProperty(String providerName, String constraintType, Property constraintProperty) {
        if (providerName != null && constraintType != null && constraintProperty != null) {
            roomConstraintPropertiesCache.computeIfAbsent(providerName, k -> new HashMap<>())
                    .put(constraintType, constraintProperty);
        }
    }

    // Note: Consumer value properties are now handled by addValuePropertiesToRoomDiagram method

    // Note: Consumer collection is now handled by addValuePropertiesToRoomDiagram method

    /**
     * Checks if a consumer is connected to any power provider (room-level or cabinet-level)
     * @param consumerName The consumer name to check
     * @return true if the consumer is connected to at least one provider, false otherwise
     */
    private boolean isConsumerConnectedToProvider(String consumerName) {
        if (consumerName == null) {
            return false;
        }

        // Check all room-level providers (including their child providers)
        for (String providerName : providerHierarchyCache.keySet()) {
            // Check parent provider
            List<String> consumers = registry.getConsumersForProvider(providerName);
            if (consumers.contains(consumerName)) {
                return true;
            }

            // Check child providers
            List<String> childProviders = providerHierarchyCache.get(providerName);
            if (childProviders != null) {
                for (String childProviderName : childProviders) {
                    List<String> childConsumers = registry.getConsumersForProvider(childProviderName);
                    if (childConsumers.contains(consumerName)) {
                        return true;
                    }
                }
            }
        }

        // Check all cabinet providers
        for (String cabinetName : cabinetNames) {
            CabinetDiagramContext cabinetContext = getCabinetContext(cabinetName);
            if (cabinetContext != null) {
                List<String> cabinetProviders = cabinetContext.getProviders();
                for (String providerName : cabinetProviders) {
                    List<String> consumers = registry.getConsumersForProvider(providerName);
                    if (consumers.contains(consumerName)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Gets room-level power consumer properties from non-cabinet properties
     * @param nonCabinetProps Pre-retrieved non-cabinet properties for the room
     * @return Map of consumer name to Property object for room-level consumers
     */
    private Map<String, Property> getRoomPowerConsumerProperties(Map<String, Property> nonCabinetProps) {
        Map<String, Property> roomConsumerProps = new HashMap<>();

        // Filter for power consumers using the registry's element type map
        for (Map.Entry<String, Property> entry : nonCabinetProps.entrySet()) {
            String elementName = entry.getKey();
            Property property = entry.getValue();

            // Check if this property is a power consumer
            String elementType = registry.getElementType(elementName);
            if (Utilities.TYPE_POWER_CONSUMER.equals(elementType)) {
                roomConsumerProps.put(elementName, property);
            }
        }

        return roomConsumerProps;
    }

    // Note: Value properties for consumers are now handled by addValuePropertiesToElement method

    /**
     * Finds the presentation element for a consumer property in the diagram
     * @param diagram The diagram to search
     * @param consumerProperty The consumer property to find
     * @return The presentation element, or null if not found
     */
    private PresentationElement findConsumerPresentationElement(DiagramPresentationElement diagram,
                                                              Property consumerProperty) {
        if (consumerProperty == null) {
            return null;
        }

        String consumerName = consumerProperty.getName();

        // First, try to get from cached consumer presentation elements
        PresentationElement cachedPE = consumerPresentationElements.get(consumerName);
        if (cachedPE != null && cachedPE.getElement() == consumerProperty) {
            return cachedPE;
        }

        // Fallback: search recursively (for room-level consumers that might not be cached)
        return findPresentationElementRecursively(diagram, consumerProperty);
    }

    /**
     * Recursively searches for a presentation element that matches the given property
     * This handles elements that may be nested inside cabinet presentation elements
     *
     * @param parentElement The parent presentation element to search
     * @param targetProperty The property to find
     * @return The presentation element, or null if not found
     */
    private PresentationElement findPresentationElementRecursively(PresentationElement parentElement, Property targetProperty) {
        if (parentElement == null || targetProperty == null) {
            return null;
        }

        // Check all direct children of this presentation element
        for (PresentationElement pe : parentElement.getPresentationElements()) {
            if (pe.getElement() == targetProperty) {
                return pe;
            }

            // Recursively search this element's children
            PresentationElement found = findPresentationElementRecursively(pe, targetProperty);
            if (found != null) {
                return found;
            }
        }

        return null;
    }

    /**
     * Redefines value properties for room-level power providers in the diagram and adds them to the diagram
     * Only redefines properties for:
     * 1. Child power providers (not parent providers)
     * 2. Leaf power providers (providers that don't have children)
     * Follows the same pattern as PowerDiagramElementManager: first redefine properties, then add value properties
     *
     * @param diagram The diagram to add provider value properties to
     * @param roomProviderProps Map of room provider names to their Property objects
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    public void addProviderValuePropertiesToDiagram(DiagramPresentationElement diagram,
                                                   Map<String, Property> roomProviderProps) throws ReadOnlyElementException {
        if (diagram == null || roomProviderProps == null || roomProviderProps.isEmpty()) {
            return;
        }

        // Step 1: Collect all providers that should have value properties redefined
        // This includes child providers and leaf providers (providers without children)
        Map<String, Property> providersToProcess = getAllProvidersForValueProperties(diagram, roomProviderProps);

        if (providersToProcess.isEmpty()) {
            return;
        }

        // Step 2: Collect provider elements that need property redefinition
        Map<String, Map<String, Object>> propertiesToRedefine = new HashMap<>();
        Map<String, Map<String, Object>> allElementProps = registry.getElementProperties();

        for (Map.Entry<String, Property> entry : providersToProcess.entrySet()) {
            String providerName = entry.getKey();
            Property providerProperty = entry.getValue();

            // Check if this provider is in the diagram (has a presentation element)
            PresentationElement providerPE = findProviderPresentationElement(diagram, providerProperty);
            if (providerPE != null && providerProperty.getType() instanceof Class) {
                Class providerType = (Class) providerProperty.getType();
                Map<String, Object> providerRegistryProps = allElementProps.get(providerName);

                if (providerRegistryProps != null && !providerRegistryProps.isEmpty()) {
                    propertiesToRedefine.put(providerType.getID(), providerRegistryProps);
                }
            }
        }

        // Step 3: Redefine properties in batch
        for (Map.Entry<String, Map<String, Object>> entry : propertiesToRedefine.entrySet()) {
            String elementId = entry.getKey();
            Map<String, Object> props = entry.getValue();
            try {
                BaseElement baseElement = project.getElementByID(elementId);
                if (baseElement instanceof Element) {
                    Element element = (Element) baseElement;
                    PowerDiagramElementManager.redefineInheritedPowerProperties(project, element, props);
                }
            } catch (Exception e) {
                Log("Error redefining properties for provider element ID " + elementId + ": " + e.getMessage());
            }
        }

        // Step 4: Add value properties to the filtered provider presentation elements
        for (Map.Entry<String, Property> entry : providersToProcess.entrySet()) {
            String providerName = entry.getKey();
            Property providerProperty = entry.getValue();

            try {
                addValuePropertiesToProvider(diagram, providerName, providerProperty);
            } catch (Exception e) {
                Log("Error adding value properties for provider " + providerName + ": " + e.getMessage());
            }
        }
    }

    /**
     * Gets all providers that should have value properties redefined
     * This includes:
     * 1. Child power providers (found in parent provider blocks)
     * 2. Leaf power providers (providers that don't have children)
     * Excludes parent providers that have children
     *
     * @param diagram The diagram to search for providers
     * @param roomProviderProps Map of room provider names to their Property objects (parent providers)
     * @return Map of provider name to Property object for providers that should have value properties
     */
    private Map<String, Property> getAllProvidersForValueProperties(DiagramPresentationElement diagram,
                                                                  Map<String, Property> roomProviderProps) {
        Map<String, Property> providersToProcess = new HashMap<>();

        // Process each parent provider to find child providers and determine if parent is a leaf
        for (Map.Entry<String, Property> entry : roomProviderProps.entrySet()) {
            String parentProviderName = entry.getKey();
            Property parentProviderProperty = entry.getValue();

            // Check if this parent provider has children
            List<String> childProviders = providerHierarchyCache.get(parentProviderName);

            if (childProviders != null && !childProviders.isEmpty()) {
                // Parent has children - add child providers, not the parent
                addChildProvidersForValueProperties(diagram, parentProviderProperty, childProviders, providersToProcess);
            } else {
                // Parent has no children - it's a leaf provider, add it
                providersToProcess.put(parentProviderName, parentProviderProperty);
            }
        }

        return providersToProcess;
    }

    /**
     * Adds child providers to the collection of providers that should have value properties
     * This method finds child provider properties within the parent provider block
     *
     * @param diagram The diagram
     * @param parentProviderProperty The parent provider property
     * @param childProviders List of child provider names
     * @param providersToProcess Map to add child providers to
     */
    private void addChildProvidersForValueProperties(DiagramPresentationElement diagram,
                                                   Property parentProviderProperty,
                                                   List<String> childProviders,
                                                   Map<String, Property> providersToProcess) {
        if (!(parentProviderProperty.getType() instanceof Class)) {
            return;
        }

        Class parentProviderBlock = (Class) parentProviderProperty.getType();

        // Find each child provider property in the parent provider block
        for (String childProviderName : childProviders) {
            try {
                Property childProviderProperty = Utilities.ModelElements.findPropertyByName(parentProviderBlock, childProviderName);

                if (childProviderProperty != null) {
                    // Check if this child provider has a presentation element in the diagram
                    PresentationElement childPE = findChildProviderPresentationElement(diagram, parentProviderProperty, childProviderProperty);
                    if (childPE != null) {
                        providersToProcess.put(childProviderName, childProviderProperty);
                    }
                }
            } catch (Exception e) {
                Log("Error finding child provider property " + childProviderName + " in parent " + parentProviderProperty.getName() + ": " + e.getMessage());
            }
        }
    }

    /**
     * Finds the presentation element for a child provider within a parent provider
     * This searches within the parent provider's presentation element
     *
     * @param diagram The diagram
     * @param parentProviderProperty The parent provider property
     * @param childProviderProperty The child provider property to find
     * @return The child provider presentation element, or null if not found
     */
    private PresentationElement findChildProviderPresentationElement(DiagramPresentationElement diagram,
                                                                   Property parentProviderProperty,
                                                                   Property childProviderProperty) {
        // First find the parent provider presentation element
        PresentationElement parentPE = findProviderPresentationElement(diagram, parentProviderProperty);
        if (parentPE == null) {
            return null;
        }

        // Then search for the child provider within the parent
        return findPresentationElementRecursively(parentPE, childProviderProperty);
    }

    /**
     * Finds the presentation element for a provider property in the diagram
     * @param diagram The diagram to search
     * @param providerProperty The provider property to find
     * @return The presentation element, or null if not found
     */
    private PresentationElement findProviderPresentationElement(DiagramPresentationElement diagram,
                                                              Property providerProperty) {
        if (providerProperty == null) {
            return null;
        }

        String providerName = providerProperty.getName();

        // First, try to get from cached provider presentation elements
        PresentationElement cachedPE = providerPresentationElements.get(providerName);
        if (cachedPE != null && cachedPE.getElement() == providerProperty) {
            return cachedPE;
        }

        // Fallback: search recursively (for providers that might not be cached)
        return findPresentationElementRecursively(diagram, providerProperty);
    }

    /**
     * Adds value properties to a provider's presentation element in the diagram
     * @param diagram The diagram
     * @param providerName The provider name
     * @param providerProperty The provider property
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addValuePropertiesToProvider(DiagramPresentationElement diagram, String providerName,
                                            Property providerProperty) throws ReadOnlyElementException {
        if (!(providerProperty.getType() instanceof Class)) {
            return;
        }

        Class providerType = (Class) providerProperty.getType();

        // Find the provider's presentation element in the diagram
        PresentationElement providerPE = findProviderPresentationElement(diagram, providerProperty);
        if (providerPE == null) {
            return;
        }

        // Get provider properties from registry to determine which value properties to show
        Map<String, Map<String, Object>> allElementProps = registry.getElementProperties();
        Map<String, Object> providerRegistryProps = allElementProps.get(providerName);
        if (providerRegistryProps == null || providerRegistryProps.isEmpty()) {
            return;
        }

        // Collect value properties to display
        List<Property> valueProps = new ArrayList<>();
        Map<String, Property> valuePropsMap = new HashMap<>();
        Set<String> propNames = providerRegistryProps.keySet();

        for (Property valueProperty : providerType.getOwnedAttribute()) {
            String propName = valueProperty.getName();
            // Only add properties that are in the registry props and not ports or special properties
            if (!(valueProperty instanceof Port) && propName != null &&
                !propName.equals("host_asset") && !propName.equals("host_location") &&
                propNames.contains(propName)) {
                valueProps.add(valueProperty);
                valuePropsMap.put(propName, valueProperty);
            }
        }

        // Cache the value properties
        if (!valuePropsMap.isEmpty()) {
            cacheProviderValueProperties(providerName, valuePropsMap);
        }

        // Add value properties to the presentation element
        PresentationElementsManager manager = PresentationElementsManager.getInstance();
        for (Property valueProperty : valueProps) {
            try {
                manager.createShapeElement(valueProperty, providerPE, true);
            } catch (Exception e) {
                Log("Error adding value property '" + valueProperty.getName() +
                    "' to provider '" + providerName + "': " + e.getMessage());
            }
        }
    }


    //--------------------------------------------------------------------------
    // ROOM PROVIDER CONSTRAINT PROPERTIES METHODS
    //--------------------------------------------------------------------------

    /**
     * Adds constraint properties for room-level providers to the diagram
     * Uses PowerDiagramElementManager for consistent constraint property creation
     *
     * @param diagram The diagram to add constraint properties to
     * @param roomProviderProps Map of room provider names to their Property objects
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addConstraintPropertiesToRoomDiagram(DiagramPresentationElement diagram,
                                                     Map<String, Property> roomProviderProps) throws ReadOnlyElementException {
        try {
            // Process all room-level providers (parent providers)
            for (String parentProviderName : roomProviderProps.keySet()) {
                // Check if this parent provider has child providers
                List<String> childProviders = providerHierarchyCache.get(parentProviderName);

                if (childProviders != null && !childProviders.isEmpty()) {
                    // Parent has children - create constraint properties for each child provider
                    for (String childProviderName : childProviders) {
                        addConstraintPropertiesForProvider(childProviderName);
                    }
                } else {
                    // Parent has no children - it's a leaf provider, create constraint properties for it
                    addConstraintPropertiesForProvider(parentProviderName);
                }
            }

        } catch (Exception e) {
            Log("Error adding constraint properties to room diagram: " + e.getMessage());
        }
    }

    /**
     * Adds constraint properties for a specific room-level provider using PowerDiagramElementManager pattern
     * @param providerName The provider name
     */
    private void addConstraintPropertiesForProvider(String providerName) {
        try {
            // Get the Power Calculation package's constraint blocks
            Map<String, Class> constraintBlocks = SysMLStereotypes.getPowerCalculationConstraintBlocks();
            if (constraintBlocks.isEmpty()) {
                Log("Error: No constraint blocks found in Power Calculation package. Cannot add constraint properties for provider " + providerName);
                return;
            }

            // Create constraint properties for each constraint block type
            for (Map.Entry<String, Class> entry : constraintBlocks.entrySet()) {
                String constraintBlockName = entry.getKey();
                Class constraintBlock = entry.getValue();

                // Create constraint property using the same pattern as PowerDiagramElementManager
                createConstraintPropertyForRoomProvider(providerName, constraintBlockName, constraintBlock);
            }

        } catch (Exception e) {
            Log("Error adding constraint properties for room provider " + providerName + ": " + e.getMessage());
        }
    }

    /**
     * Creates a constraint property for a room provider using PowerDiagramElementManager pattern
     * @param providerName The provider name
     * @param constraintBlockName The constraint block name
     * @param constraintBlock The constraint block class
     */
    private void createConstraintPropertyForRoomProvider(String providerName, String constraintBlockName, Class constraintBlock) {
        // Create a unique name for this constraint property based on the provider
        String constraintPropName = providerName + "_" + constraintBlockName;

        // Check if a constraint property with this name already exists in the room block
        Property constraintProp = Utilities.ModelElements.findPropertyByName(roomBlock, constraintPropName);

        if (constraintProp == null) {
            // No existing property, create a new one owned by the room block
            try {
                constraintProp = project.getElementsFactory().createPropertyInstance();
                constraintProp.setName(constraintPropName);
                constraintProp.setType(constraintBlock);
                constraintProp.setAggregation(AggregationKindEnum.COMPOSITE);
                constraintProp.setOwner(roomBlock);
            } catch (Exception e) {
                Log("Error creating constraint property " + constraintPropName + " for room: " + e.getMessage());
                return;
            }
        }

        // Cache the constraint property for later retrieval by binding connector logic
        cacheRoomConstraintProperty(providerName, constraintBlockName, constraintProp);

        // Cache constraint ports similar to PowerDiagramElementManager
        List<Property> ports = new ArrayList<>(constraintBlock.getOwnedAttribute());
        if (!ports.isEmpty()) {
            Map<String, Property> portsMap = new HashMap<>();
            for (Property port : ports) {
                if (port.getName() != null) {
                    portsMap.put(port.getName(), port);
                }
            }

            if (!portsMap.isEmpty()) {
                cacheRoomConstraintPorts(providerName, constraintBlockName, portsMap);
            }
        }
    }

    // Note: Constraint property creation is now handled by createConstraintPropertyForRoomProvider method

    /**
     * Creates external load properties for room-level providers
     * Creates external_load properties for child providers if they exist, otherwise for leaf providers
     *
     * @param diagram The diagram to add external load properties to
     * @param roomProviderProps Map of room provider names to their Property objects
     * @throws ReadOnlyElementException If a read-only element is encountered
     */
    private void addExternalLoadPropertiesToRoomDiagram(DiagramPresentationElement diagram,
                                                       Map<String, Property> roomProviderProps) throws ReadOnlyElementException {
        List<Property> externalLoadProperties = new ArrayList<>();

        for (String parentProviderName : roomProviderProps.keySet()) {
            // Check if this parent provider has child providers
            List<String> childProviders = providerHierarchyCache.get(parentProviderName);

            if (childProviders != null && !childProviders.isEmpty()) {
                // Parent has children - create external_load properties for each child provider
                for (String childProviderName : childProviders) {
                    Property externalLoadProperty = createExternalLoadProperty(childProviderName);
                    if (externalLoadProperty != null) {
                        externalLoadProperties.add(externalLoadProperty);
                    }
                }
            } else {
                // Parent has no children - it's a leaf provider, create external_load for it
                Property externalLoadProperty = createExternalLoadProperty(parentProviderName);
                if (externalLoadProperty != null) {
                    externalLoadProperties.add(externalLoadProperty);
                }
            }
        }

        // Add all external load properties to the diagram
        if (!externalLoadProperties.isEmpty()) {
            PowerDiagramElementManager.addElementsToDiagram(null, diagram,
                externalLoadProperties.toArray(new Element[0]));
        }
    }

    /**
     * Helper method to create an external load property for a provider
     * @param providerName The provider name
     * @return The created external load property or null if creation failed
     */
    private Property createExternalLoadProperty(String providerName) {
        String propName = "external_load_" + providerName;

        // Check if external load property already exists
        Property externalLoadProperty = Utilities.ModelElements.findPropertyByName(roomBlock, propName);

        // If not found, create it
        if (externalLoadProperty == null) {
            externalLoadProperty = createPowerPropertyForRoom(project, roomBlock, propName);

            if (externalLoadProperty != null) {
                // Set default value
                try {
                    LiteralReal defaultValue = project.getElementsFactory().createLiteralRealInstance();
                    defaultValue.setValue(0.0);
                    defaultValue.setOwner(externalLoadProperty);
                    externalLoadProperty.setDefaultValue(defaultValue);
                } catch (Exception e) {
                    Log("Error setting default value for " + propName + ": " + e.getMessage());
                }
            }
        }

        return externalLoadProperty;
    }

    /**
     * Creates a power-related property on the room block with the specified name
     * This mirrors createPowerProperty from PowerDiagramElementManager
     *
     * @param project The MagicDraw project
     * @param roomBlock The room block element
     * @param propName The property name
     * @return The created Property or null if creation failed
     */
    private Property createPowerPropertyForRoom(Project project, Class roomBlock, String propName) {
        try {
            // Get the power type
            Type powerType = SysMLStereotypes.findISO80000Type("power[watt]");
            if (powerType == null) {
                Log("Warning: ISO type 'power[watt]' not found. Cannot create " + propName + " property for room " + this.contextName);
                return null;
            }

            // Create the property
            Property property = project.getElementsFactory().createPropertyInstance();
            property.setName(propName);
            property.setVisibility(VisibilityKindEnum.PUBLIC);
            property.setType(powerType);
            property.setOwner(roomBlock);

            // Add ValueProperty stereotype
            Stereotype valuePropertyStereotype = SysMLStereotypes.getValuePropertyStereotype();
            if (valuePropertyStereotype != null) {
                StereotypesHelper.addStereotype(property, valuePropertyStereotype);
            } else {
                Log("Warning: ValueProperty stereotype not found. Cannot apply to " + propName + " for room " + this.contextName);
            }

            return property;
        } catch (Exception e) {
            Log("Error creating " + propName + " property for room " + this.contextName + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Gets the room block class
     * @return The room block class
     */
    public Class getRoomBlock() {
        return roomBlock;
    }

    /**
     * Gets room-level power provider properties
     * @return Map of provider name to Property object for room-level providers
     */
    public Map<String, Property> getRoomPowerProviderProperties() {
        return new HashMap<>(roomPowerProviderPropertiesCache);
    }

    /**
     * Gets constraint properties for a specific room provider
     * @param providerName The provider name
     * @return Map of constraint type to constraint property
     */
    public Map<String, Property> getRoomConstraintProperties(String providerName) {
        // First try to get from cache
        Map<String, Property> cachedProps = roomConstraintPropertiesCache.get(providerName);
        if (cachedProps != null) {
            return new HashMap<>(cachedProps);
        }

        // If not in cache, search the room block (fallback for legacy compatibility)
        Map<String, Property> result = new HashMap<>();
        for (Property property : roomBlock.getOwnedAttribute()) {
            String propName = property.getName();
            if (propName != null && propName.startsWith(providerName + "_")) {
                // Extract constraint type from property name (e.g., "provider_Power_Total" -> "Power_Total")
                String constraintType = propName.substring(providerName.length() + 1);
                result.put(constraintType, property);
            }
        }

        return result;
    }

    /**
     * Caches constraint ports for room providers
     * @param providerName The provider name
     * @param constraintType The constraint type
     * @param ports Map of port name to port property
     */
    public void cacheRoomConstraintPorts(String providerName, String constraintType, Map<String, Property> ports) {
        // Could implement caching if needed for performance
        // For now, ports are retrieved dynamically from constraint blocks
    }

    /**
     * Gets external load property for a room provider
     * @param providerName The provider name
     * @return The external load property or null if not found
     */
    public Property getRoomExternalLoadProperty(String providerName) {
        String propName = "external_load_" + providerName;
        return Utilities.ModelElements.findPropertyByName(roomBlock, propName);
    }

    /**
     * Gets child providers for a parent provider
     * @param parentProviderName The parent provider name
     * @return List of child provider names
     */
    public List<String> getChildProvidersForParent(String parentProviderName) {
        List<String> childProviders = providerHierarchyCache.get(parentProviderName);
        return childProviders != null ? new ArrayList<>(childProviders) : new ArrayList<>();
    }

    /**
     * Gets consumers connected to a room provider (including through child providers)
     * @param providerName The provider name
     * @return List of consumer names connected to this provider
     */
    public List<String> getConsumersForRoomProvider(String providerName) {
        List<String> allConsumers = new ArrayList<>();

        // Get direct consumers
        List<String> directConsumers = registry.getConsumersForProvider(providerName);
        allConsumers.addAll(directConsumers);

        // Get consumers from child providers if this is a parent provider
        List<String> childProviders = getChildProvidersForParent(providerName);
        for (String childProvider : childProviders) {
            List<String> childConsumers = registry.getConsumersForProvider(childProvider);
            allConsumers.addAll(childConsumers);
        }

        return allConsumers;
    }

    /**
     * Gets a room consumer property by name
     * @param consumerName The consumer name
     * @return The consumer property or null if not found
     */
    public Property getRoomConsumerProperty(String consumerName) {
        // First check room-level properties
        Map<String, Property> roomNonCabinetProps = Utilities.getRoomNonCabinetProperties(this.contextName);
        Property consumerProperty = roomNonCabinetProps.get(consumerName);

        if (consumerProperty != null) {
            return consumerProperty;
        }

        // If not found in room-level, check cabinets
        for (String cabinetName : cabinetNames) {
            Map<String, Property> cabinetConsumers = cabinetConsumerCache.get(cabinetName);
            if (cabinetConsumers != null && cabinetConsumers.containsKey(consumerName)) {
                return cabinetConsumers.get(consumerName);
            }
        }

        return null;
    }

    //--------------------------------------------------------------------------
    // BINDING CONNECTOR SUPPORT METHODS
    //--------------------------------------------------------------------------

    /**
     * Gets constraint properties for a room-level provider
     * This method uses the existing getRoomConstraintProperties method
     * @param providerName The provider name
     * @return Map of constraint type to Property object
     */
    public Map<String, Property> getConstraintProperties(String providerName) {
        return getRoomConstraintProperties(providerName);
    }

    // Note: getConstraintPorts method is now implemented as interface method above

    /**
     * Gets external load property for a room-level provider
     * This method uses the existing getRoomExternalLoadProperty method
     * @param providerName The provider name
     * @return The external load Property, or null if not found
     */
    public Property getExternalLoadProperty(String providerName) {
        return getRoomExternalLoadProperty(providerName);
    }

    // Note: getPartProperty method is now inherited from PowerDiagramContext

    /**
     * Gets value properties for a room-level element
     * @param elementName The element name
     * @return Map of property name to Property object
     */
    public Map<String, Property> getValueProperties(String elementName) {
        if (elementName == null) {
            return new HashMap<>();
        }

        // Check cached provider value properties first
        Map<String, Property> cachedProps = roomProviderValuePropertiesCache.get(elementName);
        if (cachedProps != null) {
            return new HashMap<>(cachedProps);
        }

        // Check cached consumer value properties
        cachedProps = roomConsumerValuePropertiesCache.get(elementName);
        if (cachedProps != null) {
            return new HashMap<>(cachedProps);
        }

        // If not cached, return empty map (value properties should be cached during diagram creation)
        return new HashMap<>();
    }

    // Note: getConsumersForProvider and isTopProvider methods are now implemented as interface methods above

    /**
     * Gets child providers for a top provider
     * This method uses the existing getChildProvidersForParent method
     * @param topProviderName The top provider name
     * @return List of child provider names
     */
    public List<String> getChildProvidersForTop(String topProviderName) {
        return getChildProvidersForParent(topProviderName);
    }
}
